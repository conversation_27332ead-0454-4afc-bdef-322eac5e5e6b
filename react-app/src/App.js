
import React, { useEffect } from 'react';
import './index.css';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import Featured from './components/Featured';
import About from './components/About';
import Experience from './components/Experience';
import Skills from './components/Skills';
import Education from './components/Education';
import Publications from './components/Publications';
import Contact from './components/Contact';
import Footer from './components/Footer';
import { useSmoothScroll } from './hooks';

function App() {
  useSmoothScroll();

  useEffect(() => {
    document.body.setAttribute('data-color-scheme', 'dark');
  }, []);

  return (
    <div className="App">
      <Navbar />
      <Hero />
      <Featured />
      <About />
      <Experience />
      <Skills />
      <Education />
      <Publications />
      <Contact />
      <Footer />
    </div>
  );
}

export default App;
