
import React from 'react';
import { useInView } from 'react-intersection-observer';

const FadeInUp = ({ children, delay = 0 }) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div
      ref={ref}
      className={inView ? 'fade-in-up' : ''}
      style={{ animationDelay: `${delay}s` }}
    >
      {children}
    </div>
  );
};

export default FadeInUp;
