
import React, { useState } from 'react';
import resumeData from '../services/resumeData';

const Contact = () => {
  const basics = resumeData.getBasics();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log(formData);
    alert('Thank you for your message!');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  return (
    <section id="contact" className="contact">
      <div className="container">
        <h2 className="section-title">Get In Touch</h2>
        <div className="contact-content">
          <div className="contact-info-section">
            <h3>Contact Information</h3>
            <div className="contact-details">
              <div className="contact-detail">
                <strong>Location:</strong> {basics.location?.city}, {basics.location?.region}, {basics.location?.countryCode}
              </div>
              <div className="contact-detail">
                <strong>LinkedIn:</strong> <a href={basics.profiles?.find(p => p.network === 'LinkedIn')?.url} target="_blank" rel="noopener noreferrer">{basics.profiles?.find(p => p.network === 'LinkedIn')?.url}</a>
              </div>
            </div>
          </div>
          <div className="contact-form-section">
            <h3>Send a Message</h3>
            <form className="contact-form" id="contact-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name" className="form-label">Name</label>
                <input type="text" id="name" name="name" className="form-control" required value={formData.name} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email</label>
                <input type="email" id="email" name="email" className="form-control" required value={formData.email} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="subject" className="form-label">Subject</label>
                <input type="text" id="subject" name="subject" className="form-control" required value={formData.subject} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="message" className="form-label">Message</label>
                <textarea id="message" name="message" rows="5" className="form-control" required value={formData.message} onChange={handleChange}></textarea>
              </div>
              <button type="submit" className="btn btn--primary btn--full-width">Send Message</button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
