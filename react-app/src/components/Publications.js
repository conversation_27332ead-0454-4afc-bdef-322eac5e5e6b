
import React from 'react';
import FadeInUp from './FadeInUp';
import resumeData from '../services/resumeData';

const Publications = () => {
  const publications = resumeData.getFormattedPublications();

  return (
    <section id="publications" className="publications">
      <div className="container">
        <h2 className="section-title">Publications & Thought Leadership</h2>
        <div className="publications-grid">
          {publications.map((pub, index) => (
            <FadeInUp key={index} delay={index * 0.1}>
              <div className="publication-item">
                <h3>{pub.name}</h3>
                {pub.publisher && <p className="publisher">{pub.publisher}</p>}
                {pub.releaseDate && <span className="publication-date">{pub.releaseDate}</span>}
                {pub.url && (
                  <a href={pub.url} target="_blank" rel="noopener noreferrer" className="publication-link">
                    Read More
                  </a>
                )}
              </div>
            </FadeInUp>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Publications;
