
import React from 'react';
import FadeInUp from './FadeInUp';
import resumeData from '../services/resumeData';
import { FaCloud, FaCogs, FaServer, FaBrain, FaCode, FaChartLine, FaSitemap } from 'react-icons/fa';

const Skills = () => {
  const skills = resumeData.getFormattedSkills();

  const categoryIcons = {
    "Cloud & Virtualization": <FaCloud />,
    "DevOps & Containerization": <FaCogs />,
    "Operating Systems & Infrastructure": <FaServer />,
    "AI & Data": <FaBrain />,
    "Software Development & Methodologies": <FaCode />,
    "Business & Strategy": <FaChartLine />,
    "Architecture & Core Engineering": <FaSitemap />,
  };

  return (
    <section id="skills" className="skills">
      <div className="container">
        <h2 className="section-title">Technical Skills</h2>
        <div className="skills-grid">
          {Object.keys(skills).map((category, index) => (
            <FadeInUp key={index}>
              <div className="skill-category">
                <h3 className="skill-category-title">
                  {categoryIcons[category]} {category}
                </h3>
                <div className="skill-list">
                  {skills[category].map((skill, i) => (
                    <span className="skill-tag" key={i}>{skill}</span>
                  ))}
                </div>
              </div>
            </FadeInUp>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
